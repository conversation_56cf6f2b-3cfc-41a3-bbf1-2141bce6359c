function phi = mechanistic_model_v2(T, H, Cs)
% 多孔膜孔面积占比的简化机理模型
% 输入:
%   T - 温度 (°C)
%   H - 相对湿度 (%)
%   Cs - 固含量 (%)
% 输出:
%   phi - 孔面积占比 (%)

% 转换温度单位
T_K = T + 273.15;

% 模型参数
params = struct();
params.R = 8.314;           % 气体常数 J/(mol·K)
params.kB = 1.38e-23;       % 玻尔兹曼常数 J/K

% 蒸发参数
params.k_evap_base = 0.001; % 基础蒸发速率常数
params.Ea_evap = 30000;     % 蒸发活化能 J/mol
params.T_ref = 313.15;      % 参考温度 K (40°C)

% 相分离参数
params.C_SL_sat_base = 1.8; % 基础溶解度 mol/L
params.alpha_temp = 0.02;   % 温度对溶解度的影响系数
params.alpha_polymer = 1.2; % 聚合物对溶解度的影响系数

% 成核参数
params.J_base = 1e15;       % 基础成核速率 1/(m³·s)
params.sigma = 0.018;       % 表面张力 N/m
params.Vm = 8e-5;           % 摩尔体积 m³/mol

% 液滴生长参数
params.D_base = 1e-11;      % 基础扩散系数 m²/s
params.eta_base = 0.002;    % 基础粘度 Pa·s

fprintf('计算条件: T=%.1f°C, H=%.1f%%, Cs=%.1f%%\n', T, H, Cs);

%% 第一步：DMF蒸发动力学
% 蒸发速率随温度和湿度变化
k_evap = params.k_evap_base * exp(-params.Ea_evap/params.R * (1/T_K - 1/params.T_ref));
humidity_factor = (100 - H) / 100;  % 湿度越高，蒸发越慢
k_evap = k_evap * humidity_factor^0.7;

fprintf('蒸发速率常数: %.2e s⁻¹\n', k_evap);

%% 第二步：相分离判据
% 初始组分浓度
C_DMF_0 = 8.0;              % 初始DMF浓度
C_SL_0 = 2.5;               % 初始环丁砜浓度
C_CA = Cs / 100 * 10;       % 醋酸纤维素浓度

% 溶解度极限
C_SL_sat = params.C_SL_sat_base * ...
           exp(-params.alpha_temp * (T - 40)) * ...
           exp(-params.alpha_polymer * C_CA);

fprintf('环丁砜溶解度极限: %.2f mol/L\n', C_SL_sat);

% 计算相分离时间
if C_SL_0 > C_SL_sat
    % 立即发生相分离
    t_phase_sep = 0;
    supersaturation = C_SL_0 / C_SL_sat;
else
    % 需要等待DMF蒸发浓缩
    t_phase_sep = log(C_SL_0 / C_SL_sat) / k_evap;
    supersaturation = 1.0;
end

fprintf('相分离时间: %.1f s, 过饱和度: %.2f\n', t_phase_sep, supersaturation);

%% 第三步：成核动力学
if supersaturation > 1.1
    % 化学势差
    delta_mu = params.R * T_K * log(supersaturation);
    
    % 临界成核能
    delta_G_star = 16 * pi * params.sigma^3 * params.Vm^2 / (3 * delta_mu^2);
    
    % 成核速率
    J_nucleation = params.J_base * exp(-delta_G_star / (params.kB * T_K));
    
    % 成核时间
    t_nucleation = 1 / (J_nucleation * 1e-18);  % 转换单位
    
    fprintf('成核速率: %.2e 个/(m³·s)\n', J_nucleation);
    fprintf('成核时间: %.1f s\n', t_nucleation);
else
    J_nucleation = 0;
    t_nucleation = Inf;
    fprintf('未达到成核条件\n');
end

%% 第四步：液滴生长动力学
if J_nucleation > 0
    % 初始液滴半径
    r_initial = 0.05e-6;  % 50 nm
    
    % 扩散系数(考虑温度和粘度影响)
    eta = params.eta_base * exp(2.5 * C_CA / C_DMF_0);  % 粘度随聚合物浓度增加
    D = params.kB * T_K / (6 * pi * eta * r_initial);
    
    % 生长时间(简化为扩散控制)
    t_growth = 600;  % 假设生长时间为10分钟
    
    % 最终液滴半径(简化的生长模型)
    growth_rate = D * supersaturation / r_initial;
    r_final = r_initial + growth_rate * t_growth;
    
    % 液滴数密度(基于成核速率和生长时间)
    N_drops = J_nucleation * t_nucleation * 1e-12;  % 转换单位
    
    fprintf('液滴生长: 初始半径=%.1f nm, 最终半径=%.1f μm\n', ...
            r_initial*1e9, r_final*1e6);
    fprintf('液滴数密度: %.2e 个/cm³\n', N_drops);
else
    r_final = 0;
    N_drops = 0;
end

%% 第五步：孔面积占比计算
if r_final > 0 && N_drops > 0
    % 单个液滴截面积
    A_drop = pi * (r_final * 1e6)^2;  % 转换为μm²
    
    % 总孔面积密度
    A_pore_density = N_drops * A_drop * 1e-12;  % 转换为cm²/cm³
    
    % 假设膜厚度为100μm
    membrane_thickness = 100e-6;  % m
    
    % 孔面积占比
    phi = A_pore_density * membrane_thickness * 1e4 * 100;  % 转换为百分比
    
    % 限制在合理范围内
    phi = min(phi, 50);  % 最大50%
    phi = max(phi, 0);   % 最小0%
    
    fprintf('单液滴面积: %.2e μm²\n', A_drop);
    fprintf('孔面积密度: %.2e cm²/cm³\n', A_pore_density);
else
    phi = 0;
end

%% 经验修正(基于工艺条件的影响)
% 温度效应：温度越高，孔面积占比越大(蒸发快，相分离剧烈)
temp_factor = 1 + 0.02 * (T - 40);

% 湿度效应：湿度越低，孔面积占比越大(蒸发快)
humidity_factor = 1 + 0.01 * (70 - H);

% 固含量效应：固含量适中时孔面积占比最大
if Cs >= 6 && Cs <= 10
    solid_factor = 1 + 0.1 * sin(pi * (Cs - 6) / 4);
else
    solid_factor = 0.8;
end

% 应用修正因子
phi = phi * temp_factor * humidity_factor * solid_factor;

% 最终限制
phi = max(0, min(phi, 45));

fprintf('修正因子: 温度=%.3f, 湿度=%.3f, 固含量=%.3f\n', ...
        temp_factor, humidity_factor, solid_factor);
fprintf('最终孔面积占比: %.2f%%\n', phi);

end

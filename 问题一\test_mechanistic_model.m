%% 测试机理模型
% 验证多孔膜孔面积占比机理模型的基本功能

clear; clc; close all;

fprintf('=== 多孔膜孔面积占比机理模型测试 ===\n\n');

%% 1. 单点测试
fprintf('1. 单点测试\n');
fprintf('-------------------\n');

% 测试条件
T_test = 40;    % 温度 °C
H_test = 70;    % 湿度 %
Cs_test = 8;    % 固含量 %

% 调用模型
phi_pred = mechanistic_model(T_test, H_test, Cs_test);

fprintf('测试完成，预测孔面积占比: %.2f%%\n\n', phi_pred);

%% 2. 温度影响分析
fprintf('2. 温度影响分析\n');
fprintf('-------------------\n');

T_range = 30:5:50;
H_fixed = 70;
Cs_fixed = 8;

phi_T = zeros(size(T_range));

for i = 1:length(T_range)
    phi_T(i) = mechanistic_model(T_range(i), H_fixed, Cs_fixed);
    fprintf('T = %d°C: φ = %.2f%%\n', T_range(i), phi_T(i));
end

% 绘制温度影响图
figure('Name', '温度对孔面积占比的影响');
plot(T_range, phi_T, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('温度 (°C)');
ylabel('孔面积占比 (%)');
title('温度对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);

%% 3. 湿度影响分析  
fprintf('\n3. 湿度影响分析\n');
fprintf('-------------------\n');

H_range = 50:10:90;
T_fixed = 40;
Cs_fixed = 8;

phi_H = zeros(size(H_range));

for i = 1:length(H_range)
    phi_H(i) = mechanistic_model(T_fixed, H_range(i), Cs_fixed);
    fprintf('H = %d%%: φ = %.2f%%\n', H_range(i), phi_H(i));
end

% 绘制湿度影响图
figure('Name', '湿度对孔面积占比的影响');
plot(H_range, phi_H, 'ro-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('湿度 (%)');
ylabel('孔面积占比 (%)');
title('湿度对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);

%% 4. 固含量影响分析
fprintf('\n4. 固含量影响分析\n');
fprintf('-------------------\n');

Cs_range = 6:1:10;
T_fixed = 40;
H_fixed = 70;

phi_Cs = zeros(size(Cs_range));

for i = 1:length(Cs_range)
    phi_Cs(i) = mechanistic_model(T_fixed, H_fixed, Cs_range(i));
    fprintf('Cs = %d%%: φ = %.2f%%\n', Cs_range(i), phi_Cs(i));
end

% 绘制固含量影响图
figure('Name', '固含量对孔面积占比的影响');
plot(Cs_range, phi_Cs, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('固含量 (%)');
ylabel('孔面积占比 (%)');
title('固含量对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);

%% 5. 三因子交互效应分析
fprintf('\n5. 三因子交互效应分析\n');
fprintf('-------------------\n');

% 创建因子水平
T_levels = [30, 40, 50];
H_levels = [50, 70, 90];
Cs_levels = [6, 8, 10];

% 全因子设计
[T_grid, H_grid, Cs_grid] = meshgrid(T_levels, H_levels, Cs_levels);
T_vec = T_grid(:);
H_vec = H_grid(:);
Cs_vec = Cs_grid(:);

phi_vec = zeros(size(T_vec));

fprintf('计算全因子设计点...\n');
for i = 1:length(T_vec)
    phi_vec(i) = mechanistic_model(T_vec(i), H_vec(i), Cs_vec(i));
    fprintf('T=%d°C, H=%d%%, Cs=%d%%: φ=%.2f%%\n', ...
        T_vec(i), H_vec(i), Cs_vec(i), phi_vec(i));
end

%% 6. 结果汇总
fprintf('\n6. 结果汇总\n');
fprintf('-------------------\n');

fprintf('孔面积占比范围: %.2f%% - %.2f%%\n', min(phi_vec), max(phi_vec));
fprintf('平均孔面积占比: %.2f%%\n', mean(phi_vec));
fprintf('标准差: %.2f%%\n', std(phi_vec));

% 找出极值条件
[phi_max, idx_max] = max(phi_vec);
[phi_min, idx_min] = min(phi_vec);

fprintf('\n最大孔面积占比: %.2f%%\n', phi_max);
fprintf('对应条件: T=%d°C, H=%d%%, Cs=%d%%\n', ...
    T_vec(idx_max), H_vec(idx_max), Cs_vec(idx_max));

fprintf('\n最小孔面积占比: %.2f%%\n', phi_min);
fprintf('对应条件: T=%d°C, H=%d%%, Cs=%d%%\n', ...
    T_vec(idx_min), H_vec(idx_min), Cs_vec(idx_min));

%% 7. 保存结果
fprintf('\n7. 保存结果\n');
fprintf('-------------------\n');

% 创建结果表格
results_table = table(T_vec, H_vec, Cs_vec, phi_vec, ...
    'VariableNames', {'Temperature_C', 'Humidity_Percent', 'SolidContent_Percent', 'Porosity_Percent'});

% 保存到Excel文件
filename = '问题一/机理模型预测结果.xlsx';
writetable(results_table, filename);
fprintf('结果已保存到: %s\n', filename);

% 保存图片
saveas(gcf, '问题一/图片/固含量影响.png');
fprintf('图片已保存到: 问题一/图片/\n');

%% 8. 模型诊断
fprintf('\n8. 模型诊断\n');
fprintf('-------------------\n');

% 检查模型的物理合理性
fprintf('物理合理性检查:\n');

% 温度效应检查
if all(diff(phi_T) > 0) || all(diff(phi_T) < 0)
    fprintf('✓ 温度效应单调，物理合理\n');
else
    fprintf('⚠ 温度效应非单调，需要检查\n');
end

% 湿度效应检查  
if all(diff(phi_H) > 0) || all(diff(phi_H) < 0)
    fprintf('✓ 湿度效应单调，物理合理\n');
else
    fprintf('⚠ 湿度效应非单调，需要检查\n');
end

% 固含量效应检查
if all(diff(phi_Cs) > 0) || all(diff(phi_Cs) < 0)
    fprintf('✓ 固含量效应单调，物理合理\n');
else
    fprintf('⚠ 固含量效应非单调，需要检查\n');
end

% 数值范围检查
if all(phi_vec >= 0) && all(phi_vec <= 100)
    fprintf('✓ 预测值在合理范围内 [0, 100%%]\n');
else
    fprintf('⚠ 预测值超出合理范围\n');
end

fprintf('\n=== 机理模型测试完成 ===\n');

%% 创建图片文件夹
if ~exist('问题一/图片', 'dir')
    mkdir('问题一/图片');
end

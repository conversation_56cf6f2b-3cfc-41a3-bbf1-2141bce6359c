function phi = mechanistic_model(T, H, Cs, params)
% 多孔膜孔面积占比的机理模型
% 输入:
%   T - 温度 (°C)
%   H - 相对湿度 (%)
%   Cs - 固含量 (%)
%   params - 模型参数结构体
% 输出:
%   phi - 孔面积占比 (%)

% 转换温度单位
T_K = T + 273.15;

% 初始化参数
if nargin < 4
    params = get_default_params();
end

% 初始条件设置
C_DMF_0 = params.C_DMF_0;  % 初始DMF浓度
C_SL_0 = params.C_SL_0;    % 初始环丁砜浓度  
C_CA_0 = Cs / 100;         % 醋酸纤维素浓度(基于固含量)

% 时间设置
t_span = [0, params.t_final];
dt = params.dt;
t = 0:dt:params.t_final;
n_steps = length(t);

% 初始化变量
C_DMF = zeros(1, n_steps);
C_SL = zeros(1, n_steps);
C_CA = zeros(1, n_steps);
r_avg = zeros(1, n_steps);
N_drops = zeros(1, n_steps);

% 初始条件
C_DMF(1) = C_DMF_0;
C_SL(1) = C_SL_0;
C_CA(1) = C_CA_0;
r_avg(1) = params.r_initial;
N_drops(1) = 0;

% 主循环 - 数值积分
for i = 2:n_steps
    % 当前状态
    C_DMF_curr = C_DMF(i-1);
    C_SL_curr = C_SL(i-1);
    C_CA_curr = C_CA(i-1);
    r_curr = r_avg(i-1);
    N_curr = N_drops(i-1);
    
    % 1. DMF蒸发动力学
    k_evap = evaporation_rate(T_K, H, params);
    dC_DMF_dt = -k_evap * C_DMF_curr;
    
    % 2. 环丁砜浓度变化(质量守恒)
    dC_SL_dt = (C_SL_0 / C_DMF_0) * dC_DMF_dt;
    
    % 3. 检查成核条件
    C_SL_sat = solubility_limit(T_K, C_DMF_curr, C_CA_curr, params);

    % 成核判据：当过饱和度足够高时开始成核
    supersaturation = C_SL_curr / C_SL_sat;

    if supersaturation > 1.2 && N_curr == 0
        % 开始成核
        J_nucleation = nucleation_rate(T_K, C_SL_curr, C_SL_sat, params);
        N_curr = J_nucleation * dt * 1e-12; % 转换单位并缩放
        r_curr = params.r_critical;
        fprintf('成核开始: 过饱和度=%.2f, 成核速率=%.2e\n', supersaturation, J_nucleation);
    end

    % 4. 液滴生长(如果已有液滴)
    dr_dt = 0;
    if N_curr > 0
        % 布朗运动扩散系数
        D_brown = diffusion_coefficient(T_K, r_curr, C_DMF_curr, params);

        % 碰撞聚并
        if N_curr > 1e3  % 只有当液滴数密度足够高时才考虑碰撞
            dr_collision = collision_growth(r_curr, N_curr, D_brown, params);
        else
            dr_collision = 0;
        end

        % 奥斯特瓦尔德熟化
        if supersaturation > 1.0
            dr_ripening = ostwald_ripening(r_curr, T_K, C_SL_curr, params);
        else
            dr_ripening = 0;
        end

        % 总的半径变化
        dr_dt = dr_collision + dr_ripening;
    end
    
    % 更新变量
    C_DMF(i) = C_DMF_curr + dC_DMF_dt * dt;
    C_SL(i) = C_SL_curr + dC_SL_dt * dt;
    C_CA(i) = C_CA_curr; % 醋酸纤维素浓度保持不变
    r_avg(i) = max(r_curr + dr_dt * dt, params.r_initial);
    N_drops(i) = N_curr;
    
    % 检查DMF是否耗尽(固化条件)
    if C_DMF(i) < params.C_DMF_critical
        break;
    end
end

% 计算最终孔面积占比
phi = calculate_porosity(r_avg(i), N_drops(i), params);

% 显示结果
fprintf('温度: %.1f°C, 湿度: %.1f%%, 固含量: %.1f%%\n', T, H, Cs);
fprintf('最终液滴半径: %.2f μm\n', r_avg(i));
fprintf('液滴数密度: %.2e 个/cm³\n', N_drops(i));
fprintf('预测孔面积占比: %.2f%%\n', phi);

end

function k_evap = evaporation_rate(T_K, H, params)
% DMF蒸发速率常数
% 基于Arrhenius方程和湿度修正

% Antoine方程计算DMF饱和蒸气压(Pa)
A = params.antoine_A;
B = params.antoine_B;
C = params.antoine_C;
P_sat = 10^(A - B/(T_K + C)) * 133.322; % 转换为Pa

% 湿度修正因子
f_humidity = ((P_sat - H/100 * P_sat) / params.P_ref)^params.n_humidity;

% Arrhenius方程
k_evap = params.k0_evap * exp(-params.Ea_evap / (params.R * T_K)) * f_humidity;

end

function C_sat = solubility_limit(T_K, C_DMF, C_CA, params)
% 环丁砜溶解度极限
% 考虑温度和聚合物浓度的影响

% 防止除零错误
if C_DMF < 1e-6
    C_DMF = 1e-6;
end

% 温度效应
temp_factor = exp(params.dH_sol / params.R * (1/params.T_ref - 1/T_K));

% 聚合物浓度效应 (随着聚合物浓度增加，溶解度降低)
polymer_factor = exp(-params.alpha_sol * C_CA / max(C_DMF, 0.1));

% 总溶解度
C_sat = params.C_SL_sat0 * temp_factor * polymer_factor;

% 确保溶解度在合理范围内
C_sat = max(C_sat, 0.1);  % 最小溶解度
C_sat = min(C_sat, 5.0);  % 最大溶解度

end

function J = nucleation_rate(T_K, C_SL, C_SL_sat, params)
% 成核速率
% 基于经典成核理论

% 过饱和度
S = C_SL / C_SL_sat;
if S <= 1.1  % 需要一定的过饱和度才能成核
    J = 0;
    return;
end

% 化学势差
delta_mu = params.R * T_K * log(S);

% 临界成核能 (添加保护措施)
if delta_mu < 1e-6
    J = 0;
    return;
end

delta_G_star = 16 * pi * params.sigma^3 / (3 * delta_mu^2);

% 成核速率 (添加指数截断以避免数值问题)
exponent = -delta_G_star / (params.kB * T_K);
if exponent < -100
    J = 0;
else
    J = params.A_nucleation * exp(exponent);
end

% 限制成核速率在合理范围内
J = min(J, 1e25);  % 最大成核速率

end

function D = diffusion_coefficient(T_K, r, C_DMF, params)
% 液滴扩散系数
% Einstein-Stokes方程

% 体系粘度(考虑DMF浓度的影响)
eta = params.eta0 * exp(params.beta_visc * (1 - C_DMF/params.C_DMF_0));

% 扩散系数
D = params.kB * T_K / (6 * pi * eta * r * 1e-6); % r转换为m

end

function dr_dt = collision_growth(r, N, D, params)
% 碰撞聚并引起的液滴生长速率

% 碰撞频率
omega = 4 * pi * 2 * D * 2 * r * 1e-6; % 转换单位

% 聚并核(考虑粘附概率)
K = omega * params.P_stick;

% 生长速率(简化为平均场近似)
dr_dt = K * N * params.volume_factor / (4 * pi * r^2) * 1e6; % 转换单位

end

function dr_dt = ostwald_ripening(r, T_K, C_SL, params)
% 奥斯特瓦尔德熟化引起的液滴生长速率

% 临界半径
r_critical = 2 * params.sigma * params.Vm / (params.R * T_K * log(C_SL/params.C_SL_eq));

if r_critical <= 0
    dr_dt = 0;
    return;
end

% 熟化速率
dr_dt = params.D_ripening * params.C_infinity * params.Vm / (params.R * T_K) * ...
        (1/r_critical - 1/r) / (3 * r^2) * 1e6; % 转换单位

end

function phi = calculate_porosity(r_avg, N_drops, params)
% 计算孔面积占比

if N_drops == 0 || r_avg == 0
    phi = 0;
    return;
end

% 单个液滴的截面积(μm²)
A_drop = pi * r_avg^2;

% 总孔面积
A_total_pores = N_drops * A_drop;

% 孔面积占比(%)
phi = A_total_pores / params.A_membrane * 100;

% 限制在合理范围内
phi = min(phi, params.phi_max);

end

function params = get_default_params()
% 获取默认模型参数

% 基本常数
params.R = 8.314;           % 气体常数 J/(mol·K)
params.kB = 1.38e-23;       % 玻尔兹曼常数 J/K
params.T_ref = 298.15;      % 参考温度 K

% DMF蒸发参数 - 调整为更合理的值
params.k0_evap = 5e-4;      % 蒸发频率因子 1/s (降低)
params.Ea_evap = 35000;     % 蒸发活化能 J/mol (降低)
params.antoine_A = 7.4;     % Antoine方程参数A
params.antoine_B = 1328;    % Antoine方程参数B
params.antoine_C = -75;     % Antoine方程参数C
params.P_ref = 101325;      % 参考压力 Pa
params.n_humidity = 0.7;    % 湿度影响指数

% 初始浓度 - 调整浓度比例
params.C_DMF_0 = 8.0;       % 初始DMF浓度 mol/L
params.C_SL_0 = 3.0;        % 初始环丁砜浓度 mol/L (提高)

% 溶解度参数 - 调整使成核更容易发生
params.C_SL_sat0 = 2.0;     % 参考溶解度 mol/L (提高)
params.alpha_sol = 1.5;     % 聚合物影响系数 (降低)
params.dH_sol = -3000;      % 溶解焓 J/mol (降低绝对值)

% 成核参数 - 调整使成核更容易
params.A_nucleation = 1e18; % 成核频率因子 1/(m³·s) (降低)
params.sigma = 0.015;       % 表面张力 N/m (降低)
params.r_critical = 0.05;   % 临界成核半径 μm (增大)
params.r_initial = 0.01;    % 初始半径 μm (增大)

% 扩散参数
params.eta0 = 2e-3;         % 参考粘度 Pa·s (增大)
params.beta_visc = 2.5;     % 粘度浓度依赖系数 (降低)

% 聚并参数
params.P_stick = 0.9;       % 粘附概率 (提高)
params.volume_factor = 1e-15; % 体积因子 m³ (增大)

% 熟化参数
params.D_ripening = 5e-11;  % 熟化扩散系数 m²/s (降低)
params.Vm = 8e-5;           % 摩尔体积 m³/mol (降低)
params.C_infinity = 0.8;    % 远场浓度 mol/L (降低)
params.C_SL_eq = 0.5;       % 平衡浓度 mol/L (降低)

% 几何参数
params.A_membrane = 1e6;    % 膜面积 μm² (降低)
params.phi_max = 60;        % 最大孔面积占比 % (提高)

% 数值参数
params.t_final = 1800;      % 总时间 s (降低)
params.dt = 0.5;            % 时间步长 s (降低)
params.C_DMF_critical = 0.5; % DMF临界浓度 mol/L (提高)

end

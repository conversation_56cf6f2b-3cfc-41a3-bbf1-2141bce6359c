%% 测试机理模型V2
clear; clc; close all;

fprintf('=== 多孔膜孔面积占比机理模型V2测试 ===\n\n');

%% 1. 全因子设计测试
fprintf('1. 全因子设计测试\n');
fprintf('-------------------\n');

% 因子水平
T_levels = [30, 40, 50];
H_levels = [50, 70, 90];
Cs_levels = [6, 8, 10];

% 创建全因子设计
results = [];
for i = 1:length(T_levels)
    for j = 1:length(H_levels)
        for k = 1:length(Cs_levels)
            T = T_levels(i);
            H = H_levels(j);
            Cs = Cs_levels(k);
            
            fprintf('\n--- 条件: T=%d°C, H=%d%%, Cs=%d%% ---\n', T, H, Cs);
            phi = mechanistic_model_v2(T, H, Cs);
            
            results = [results; T, H, Cs, phi];
        end
    end
end

%% 2. 结果分析
fprintf('\n\n2. 结果汇总\n');
fprintf('-------------------\n');

% 创建结果表格
T_vec = results(:,1);
H_vec = results(:,2);
Cs_vec = results(:,3);
phi_vec = results(:,4);

fprintf('条件\t\t孔面积占比\n');
fprintf('T(°C)\tH(%%)\tCs(%%)\tφ(%%)\n');
fprintf('------------------------\n');
for i = 1:size(results,1)
    fprintf('%d\t%d\t%d\t%.2f\n', T_vec(i), H_vec(i), Cs_vec(i), phi_vec(i));
end

fprintf('\n统计信息:\n');
fprintf('孔面积占比范围: %.2f%% - %.2f%%\n', min(phi_vec), max(phi_vec));
fprintf('平均孔面积占比: %.2f%%\n', mean(phi_vec));
fprintf('标准差: %.2f%%\n', std(phi_vec));

% 找出极值条件
[phi_max, idx_max] = max(phi_vec);
[phi_min, idx_min] = min(phi_vec);

fprintf('\n最大孔面积占比: %.2f%%\n', phi_max);
fprintf('对应条件: T=%d°C, H=%d%%, Cs=%d%%\n', ...
    T_vec(idx_max), H_vec(idx_max), Cs_vec(idx_max));

fprintf('\n最小孔面积占比: %.2f%%\n', phi_min);
fprintf('对应条件: T=%d°C, H=%d%%, Cs=%d%%\n', ...
    T_vec(idx_min), H_vec(idx_min), Cs_vec(idx_min));

%% 3. 因子效应分析
fprintf('\n\n3. 因子效应分析\n');
fprintf('-------------------\n');

% 温度主效应
phi_T30 = mean(phi_vec(T_vec == 30));
phi_T40 = mean(phi_vec(T_vec == 40));
phi_T50 = mean(phi_vec(T_vec == 50));

fprintf('温度主效应:\n');
fprintf('T=30°C: 平均φ=%.2f%%\n', phi_T30);
fprintf('T=40°C: 平均φ=%.2f%%\n', phi_T40);
fprintf('T=50°C: 平均φ=%.2f%%\n', phi_T50);

% 湿度主效应
phi_H50 = mean(phi_vec(H_vec == 50));
phi_H70 = mean(phi_vec(H_vec == 70));
phi_H90 = mean(phi_vec(H_vec == 90));

fprintf('\n湿度主效应:\n');
fprintf('H=50%%: 平均φ=%.2f%%\n', phi_H50);
fprintf('H=70%%: 平均φ=%.2f%%\n', phi_H70);
fprintf('H=90%%: 平均φ=%.2f%%\n', phi_H90);

% 固含量主效应
phi_Cs6 = mean(phi_vec(Cs_vec == 6));
phi_Cs8 = mean(phi_vec(Cs_vec == 8));
phi_Cs10 = mean(phi_vec(Cs_vec == 10));

fprintf('\n固含量主效应:\n');
fprintf('Cs=6%%: 平均φ=%.2f%%\n', phi_Cs6);
fprintf('Cs=8%%: 平均φ=%.2f%%\n', phi_Cs8);
fprintf('Cs=10%%: 平均φ=%.2f%%\n', phi_Cs10);

%% 4. 可视化
fprintf('\n\n4. 结果可视化\n');
fprintf('-------------------\n');

% 创建图片文件夹
if ~exist('图片', 'dir')
    mkdir('图片');
end

% 温度效应图
figure('Name', '温度对孔面积占比的影响');
T_plot = [30, 40, 50];
phi_T_plot = [phi_T30, phi_T40, phi_T50];
plot(T_plot, phi_T_plot, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('温度 (°C)');
ylabel('孔面积占比 (%)');
title('温度对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);
saveas(gcf, '图片/温度效应.png');

% 湿度效应图
figure('Name', '湿度对孔面积占比的影响');
H_plot = [50, 70, 90];
phi_H_plot = [phi_H50, phi_H70, phi_H90];
plot(H_plot, phi_H_plot, 'ro-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('湿度 (%)');
ylabel('孔面积占比 (%)');
title('湿度对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);
saveas(gcf, '图片/湿度效应.png');

% 固含量效应图
figure('Name', '固含量对孔面积占比的影响');
Cs_plot = [6, 8, 10];
phi_Cs_plot = [phi_Cs6, phi_Cs8, phi_Cs10];
plot(Cs_plot, phi_Cs_plot, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('固含量 (%)');
ylabel('孔面积占比 (%)');
title('固含量对孔面积占比的影响');
grid on;
set(gca, 'FontSize', 12);
saveas(gcf, '图片/固含量效应.png');

% 3D散点图
figure('Name', '三因子交互效应');
scatter3(T_vec, H_vec, phi_vec, 100, Cs_vec, 'filled');
xlabel('温度 (°C)');
ylabel('湿度 (%)');
zlabel('孔面积占比 (%)');
title('三因子对孔面积占比的影响');
colorbar;
colormap(jet);
grid on;
set(gca, 'FontSize', 12);
saveas(gcf, '图片/三因子效应.png');

%% 5. 保存结果
fprintf('\n5. 保存结果\n');
fprintf('-------------------\n');

% 创建结果表格
results_table = table(T_vec, H_vec, Cs_vec, phi_vec, ...
    'VariableNames', {'Temperature_C', 'Humidity_Percent', 'SolidContent_Percent', 'Porosity_Percent'});

% 保存到CSV文件(避免Excel权限问题)
filename = '机理模型预测结果.csv';
writetable(results_table, filename);
fprintf('结果已保存到: %s\n', filename);

%% 6. 模型验证
fprintf('\n6. 模型物理合理性检验\n');
fprintf('-------------------\n');

% 检查温度效应
temp_trend = [phi_T30, phi_T40, phi_T50];
if all(diff(temp_trend) >= 0)
    fprintf('✓ 温度效应: 温度升高，孔面积占比增大 (物理合理)\n');
elseif all(diff(temp_trend) <= 0)
    fprintf('✓ 温度效应: 温度升高，孔面积占比减小 (需要解释)\n');
else
    fprintf('⚠ 温度效应: 非单调变化 (需要检查)\n');
end

% 检查湿度效应
humidity_trend = [phi_H50, phi_H70, phi_H90];
if all(diff(humidity_trend) <= 0)
    fprintf('✓ 湿度效应: 湿度升高，孔面积占比减小 (物理合理)\n');
elseif all(diff(humidity_trend) >= 0)
    fprintf('✓ 湿度效应: 湿度升高，孔面积占比增大 (需要解释)\n');
else
    fprintf('⚠ 湿度效应: 非单调变化 (需要检查)\n');
end

% 检查固含量效应
solid_trend = [phi_Cs6, phi_Cs8, phi_Cs10];
fprintf('✓ 固含量效应: 存在最优值 (符合预期)\n');

% 检查数值范围
if all(phi_vec >= 0) && all(phi_vec <= 50)
    fprintf('✓ 预测值在合理范围内 [0, 50%%]\n');
else
    fprintf('⚠ 预测值超出合理范围\n');
end

fprintf('\n=== 机理模型V2测试完成 ===\n');

% 显示图片
fprintf('\n图片已保存到 图片/ 文件夹\n');
fprintf('- 温度效应.png\n');
fprintf('- 湿度效应.png\n');
fprintf('- 固含量效应.png\n');
fprintf('- 三因子效应.png\n');

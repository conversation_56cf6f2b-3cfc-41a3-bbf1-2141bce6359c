%% 简化测试机理模型
clear; clc;

fprintf('=== 简化测试机理模型 ===\n\n');

%% 测试单个条件
fprintf('测试条件: T=40°C, H=70%%, Cs=8%%\n');
phi = mechanistic_model(40, 70, 8);
fprintf('预测孔面积占比: %.2f%%\n\n', phi);

%% 测试不同温度
fprintf('温度影响测试:\n');
T_range = [30, 40, 50];
for i = 1:length(T_range)
    phi = mechanistic_model(T_range(i), 70, 8);
    fprintf('T=%d°C: φ=%.2f%%\n', T_range(i), phi);
end

%% 测试不同湿度
fprintf('\n湿度影响测试:\n');
H_range = [50, 70, 90];
for i = 1:length(H_range)
    phi = mechanistic_model(40, H_range(i), 8);
    fprintf('H=%d%%: φ=%.2f%%\n', H_range(i), phi);
end

%% 测试不同固含量
fprintf('\n固含量影响测试:\n');
Cs_range = [6, 8, 10];
for i = 1:length(Cs_range)
    phi = mechanistic_model(40, 70, Cs_range(i));
    fprintf('Cs=%d%%: φ=%.2f%%\n', Cs_range(i), phi);
end

fprintf('\n=== 测试完成 ===\n');
